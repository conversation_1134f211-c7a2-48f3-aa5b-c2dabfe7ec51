//
//  ImagePuzzleGenerator.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

class ImagePuzzleGenerator {
    
    // MARK: - Properties
    private let pieceCount: Int
    private let targetSize: CGSize
    
    // MARK: - Initialization
    init(pieceCount: Int, targetSize: CGSize) {
        self.pieceCount = pieceCount
        self.targetSize = targetSize
    }
    
    // MARK: - Public Methods
    func generatePuzzle() -> (pieces: [PuzzlePiece], referenceImage: UIImage) {
        // For now, create a sample Turkish cultural image
        let baseImage = createSampleTurkishImage()
        
        // Generate jigsaw pieces
        let shapeGenerator = JigsawShapeGenerator(pieceCount: pieceCount, imageSize: targetSize)
        let jigsawPieces = shapeGenerator.generatePieces()
        
        var puzzlePieces: [PuzzlePiece] = []
        
        for jigsawPiece in jigsawPieces {
            // Create texture from image portion with jigsaw shape
            let texture = createTextureFromImagePortion(
                image: baseImage,
                jigsawPiece: jigsawPiece
            )
            
            // Create puzzle piece
            let piece = PuzzlePiece(
                texture: texture,
                row: jigsawPiece.row,
                col: jigsawPiece.col,
                pieceID: jigsawPiece.pieceID
            )
            
            // Set the piece size based on the jigsaw shape bounds
            let shapeBounds = jigsawPiece.shape.bounds
            piece.size = shapeBounds.size
            
            puzzlePieces.append(piece)
        }
        
        return (pieces: puzzlePieces, referenceImage: baseImage)
    }
    
    // MARK: - Private Methods
    private func createSampleTurkishImage() -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let image = renderer.image { context in
            // Create a beautiful Turkish-inspired pattern
            
            // Background gradient (Turkish flag colors)
            let colors = [UIColor.red.cgColor, UIColor(red: 0.8, green: 0.1, blue: 0.1, alpha: 1.0).cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(gradient, start: .zero, end: CGPoint(x: 0, y: targetSize.height), options: [])
            
            // Add Turkish geometric patterns
            addTurkishPatterns(to: context.cgContext, size: targetSize)
            
            // Add a crescent and star (simplified)
            addCrescentAndStar(to: context.cgContext, size: targetSize)
            
            // Add decorative border
            addDecorativeBorder(to: context.cgContext, size: targetSize)
        }
        return image
    }
    
    private func addTurkishPatterns(to context: CGContext, size: CGSize) {
        context.saveGState()
        
        // Set pattern color
        context.setFillColor(UIColor.white.withAlphaComponent(0.1).cgColor)
        
        // Create geometric patterns
        let patternSize: CGFloat = 40
        let rows = Int(size.height / patternSize) + 1
        let cols = Int(size.width / patternSize) + 1
        
        for row in 0..<rows {
            for col in 0..<cols {
                let x = CGFloat(col) * patternSize
                let y = CGFloat(row) * patternSize
                
                // Alternate between different patterns
                if (row + col) % 2 == 0 {
                    // Diamond pattern
                    let diamond = UIBezierPath()
                    diamond.move(to: CGPoint(x: x + patternSize/2, y: y))
                    diamond.addLine(to: CGPoint(x: x + patternSize, y: y + patternSize/2))
                    diamond.addLine(to: CGPoint(x: x + patternSize/2, y: y + patternSize))
                    diamond.addLine(to: CGPoint(x: x, y: y + patternSize/2))
                    diamond.close()
                    
                    context.addPath(diamond.cgPath)
                    context.fillPath()
                } else {
                    // Circle pattern
                    let circle = UIBezierPath(ovalIn: CGRect(x: x + patternSize/4, y: y + patternSize/4, width: patternSize/2, height: patternSize/2))
                    context.addPath(circle.cgPath)
                    context.fillPath()
                }
            }
        }
        
        context.restoreGState()
    }
    
    private func addCrescentAndStar(to context: CGContext, size: CGSize) {
        context.saveGState()
        
        let centerX = size.width * 0.3
        let centerY = size.height * 0.3
        
        // Draw crescent
        context.setFillColor(UIColor.white.cgColor)
        
        let crescentRadius: CGFloat = 30
        let crescentPath = UIBezierPath(arcCenter: CGPoint(x: centerX, y: centerY), radius: crescentRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        
        // Create inner circle to make crescent shape
        let innerRadius: CGFloat = 20
        let innerPath = UIBezierPath(arcCenter: CGPoint(x: centerX + 8, y: centerY), radius: innerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        crescentPath.append(innerPath.reversing())
        
        context.addPath(crescentPath.cgPath)
        context.fillPath()
        
        // Draw star
        let starCenter = CGPoint(x: centerX + 45, y: centerY - 10)
        let starPath = createStarPath(center: starCenter, radius: 15, points: 5)
        context.addPath(starPath.cgPath)
        context.fillPath()
        
        context.restoreGState()
    }
    
    private func addDecorativeBorder(to context: CGContext, size: CGSize) {
        context.saveGState()
        
        context.setStrokeColor(UIColor.white.withAlphaComponent(0.3).cgColor)
        context.setLineWidth(3)
        
        // Outer border
        let borderRect = CGRect(x: 10, y: 10, width: size.width - 20, height: size.height - 20)
        context.stroke(borderRect)
        
        // Inner decorative lines
        let innerBorder = CGRect(x: 20, y: 20, width: size.width - 40, height: size.height - 40)
        context.stroke(innerBorder)
        
        context.restoreGState()
    }
    
    private func createStarPath(center: CGPoint, radius: CGFloat, points: Int) -> UIBezierPath {
        let path = UIBezierPath()
        let angleIncrement = .pi * 2 / CGFloat(points * 2)
        
        for i in 0..<(points * 2) {
            let angle = CGFloat(i) * angleIncrement - .pi / 2
            let currentRadius = i % 2 == 0 ? radius : radius * 0.5
            let x = center.x + cos(angle) * currentRadius
            let y = center.y + sin(angle) * currentRadius
            
            if i == 0 {
                path.move(to: CGPoint(x: x, y: y))
            } else {
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        path.close()
        return path
    }
    
    private func createTextureFromImagePortion(image: UIImage, jigsawPiece: JigsawPieceInfo) -> SKTexture {
        let shapeBounds = jigsawPiece.shape.bounds
        let renderer = UIGraphicsImageRenderer(size: shapeBounds.size)
        
        let textureImage = renderer.image { context in
            // Create clipping path from jigsaw shape
            let translatedPath = UIBezierPath(cgPath: jigsawPiece.shape.cgPath)
            translatedPath.apply(CGAffineTransform(translationX: -shapeBounds.minX, y: -shapeBounds.minY))
            
            context.cgContext.addPath(translatedPath.cgPath)
            context.cgContext.clip()
            
            // Draw the image portion
            let imageRect = CGRect(
                x: -jigsawPiece.imageRect.minX - shapeBounds.minX,
                y: -jigsawPiece.imageRect.minY - shapeBounds.minY,
                width: targetSize.width,
                height: targetSize.height
            )
            
            image.draw(in: imageRect)
            
            // Add subtle border to piece
            context.cgContext.setStrokeColor(UIColor.black.withAlphaComponent(0.3).cgColor)
            context.cgContext.setLineWidth(1)
            context.cgContext.addPath(translatedPath.cgPath)
            context.cgContext.strokePath()
        }
        
        return SKTexture(image: textureImage)
    }
}
