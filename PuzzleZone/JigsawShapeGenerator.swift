//
//  JigsawShapeGenerator.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

struct JigsawPieceInfo {
    let row: Int
    let col: Int
    let pieceID: Int
    let shape: UIBezierPath
    let imageRect: CGRect
    let hasTopTab: Bool
    let hasRightTab: Bool
    let hasBottomTab: Bool
    let hasLeftTab: Bool
}

class JigsawShapeGenerator {
    
    // MARK: - Properties
    private let pieceCount: Int
    private let imageSize: CGSize
    private let gridRows: Int
    private let gridCols: Int
    private let pieceWidth: CGFloat
    private let pieceHeight: CGFloat
    
    // Tab configuration
    private let tabSize: CGFloat = 20
    private let tabCurveRadius: CGFloat = 15
    
    // MARK: - Initialization
    init(pieceCount: Int, imageSize: CGSize) {
        self.pieceCount = pieceCount
        self.imageSize = imageSize
        
        // Calculate optimal grid dimensions
        let aspectRatio = imageSize.width / imageSize.height
        let idealCols = sqrt(Double(pieceCount) * Double(aspectRatio))
        let idealRows = sqrt(Double(pieceCount) / Double(aspectRatio))
        
        self.gridCols = max(2, Int(round(idealCols)))
        self.gridRows = max(2, Int(round(idealRows)))
        
        self.pieceWidth = imageSize.width / CGFloat(gridCols)
        self.pieceHeight = imageSize.height / CGFloat(gridRows)
        
        print("🧩 Jigsaw Generator: \(pieceCount) pieces in \(gridRows)x\(gridCols) grid")
        print("📏 Piece size: \(pieceWidth) x \(pieceHeight)")
    }
    
    // MARK: - Public Methods
    func generatePieces() -> [JigsawPieceInfo] {
        var pieces: [JigsawPieceInfo] = []
        var tabPattern = generateTabPattern()
        
        for row in 0..<gridRows {
            for col in 0..<gridCols {
                let pieceID = row * gridCols + col
                
                // Skip if we've reached the desired piece count
                if pieceID >= pieceCount {
                    break
                }
                
                let piece = createPiece(row: row, col: col, pieceID: pieceID, tabPattern: tabPattern)
                pieces.append(piece)
            }
        }
        
        return pieces
    }
    
    // MARK: - Private Methods
    private func generateTabPattern() -> [[Bool]] {
        // Generate random tab pattern for horizontal and vertical connections
        var horizontalTabs = Array(repeating: Array(repeating: false, count: gridCols - 1), count: gridRows)
        var verticalTabs = Array(repeating: Array(repeating: false, count: gridCols), count: gridRows - 1)
        
        // Randomly assign tabs
        for row in 0..<gridRows {
            for col in 0..<(gridCols - 1) {
                horizontalTabs[row][col] = Bool.random()
            }
        }
        
        for row in 0..<(gridRows - 1) {
            for col in 0..<gridCols {
                verticalTabs[row][col] = Bool.random()
            }
        }
        
        return [horizontalTabs.flatMap { $0 }, verticalTabs.flatMap { $0 }]
    }
    
    private func createPiece(row: Int, col: Int, pieceID: Int, tabPattern: [[Bool]]) -> JigsawPieceInfo {
        let x = CGFloat(col) * pieceWidth
        let y = CGFloat(row) * pieceHeight
        
        // Determine which sides have tabs
        let hasTopTab = row > 0 ? tabPattern[1][(row - 1) * gridCols + col] : false
        let hasBottomTab = row < gridRows - 1 ? tabPattern[1][row * gridCols + col] : false
        let hasLeftTab = col > 0 ? tabPattern[0][row * (gridCols - 1) + (col - 1)] : false
        let hasRightTab = col < gridCols - 1 ? tabPattern[0][row * (gridCols - 1) + col] : false
        
        // Create the piece shape
        let shape = createPieceShape(
            baseRect: CGRect(x: 0, y: 0, width: pieceWidth, height: pieceHeight),
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )
        
        // Image rect for this piece
        let imageRect = CGRect(x: x, y: y, width: pieceWidth, height: pieceHeight)
        
        return JigsawPieceInfo(
            row: row,
            col: col,
            pieceID: pieceID,
            shape: shape,
            imageRect: imageRect,
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )
    }
    
    private func createPieceShape(baseRect: CGRect, hasTopTab: Bool, hasRightTab: Bool, hasBottomTab: Bool, hasLeftTab: Bool) -> UIBezierPath {
        let path = UIBezierPath()
        
        // Start from top-left corner
        var currentPoint = CGPoint(x: baseRect.minX, y: baseRect.minY)
        path.move(to: currentPoint)
        
        // Top edge
        if hasTopTab {
            addTabToPath(path, from: currentPoint, to: CGPoint(x: baseRect.maxX, y: baseRect.minY), isOutward: true, isHorizontal: true)
        } else {
            path.addLine(to: CGPoint(x: baseRect.maxX, y: baseRect.minY))
        }
        
        // Right edge
        currentPoint = CGPoint(x: baseRect.maxX, y: baseRect.minY)
        if hasRightTab {
            addTabToPath(path, from: currentPoint, to: CGPoint(x: baseRect.maxX, y: baseRect.maxY), isOutward: true, isHorizontal: false)
        } else {
            path.addLine(to: CGPoint(x: baseRect.maxX, y: baseRect.maxY))
        }
        
        // Bottom edge
        currentPoint = CGPoint(x: baseRect.maxX, y: baseRect.maxY)
        if hasBottomTab {
            addTabToPath(path, from: currentPoint, to: CGPoint(x: baseRect.minX, y: baseRect.maxY), isOutward: false, isHorizontal: true)
        } else {
            path.addLine(to: CGPoint(x: baseRect.minX, y: baseRect.maxY))
        }
        
        // Left edge
        currentPoint = CGPoint(x: baseRect.minX, y: baseRect.maxY)
        if hasLeftTab {
            addTabToPath(path, from: currentPoint, to: CGPoint(x: baseRect.minX, y: baseRect.minY), isOutward: false, isHorizontal: false)
        } else {
            path.addLine(to: CGPoint(x: baseRect.minX, y: baseRect.minY))
        }
        
        path.close()
        return path
    }
    
    private func addTabToPath(_ path: UIBezierPath, from startPoint: CGPoint, to endPoint: CGPoint, isOutward: Bool, isHorizontal: Bool) {
        let midPoint = CGPoint(
            x: (startPoint.x + endPoint.x) / 2,
            y: (startPoint.y + endPoint.y) / 2
        )
        
        let tabDirection: CGFloat = isOutward ? 1 : -1
        let tabOffset = tabSize * tabDirection
        
        var tabCenter: CGPoint
        if isHorizontal {
            tabCenter = CGPoint(x: midPoint.x, y: midPoint.y + tabOffset)
        } else {
            tabCenter = CGPoint(x: midPoint.x + tabOffset, y: midPoint.y)
        }
        
        // Create tab as a simple semicircle
        let tabRadius = tabSize * 0.6
        
        if isHorizontal {
            // Horizontal tab
            let tabStart = CGPoint(x: midPoint.x - tabRadius, y: startPoint.y)
            let tabEnd = CGPoint(x: midPoint.x + tabRadius, y: startPoint.y)
            
            path.addLine(to: tabStart)
            path.addArc(withCenter: tabCenter, radius: tabRadius, startAngle: isOutward ? .pi : 0, endAngle: isOutward ? 0 : .pi, clockwise: !isOutward)
            path.addLine(to: endPoint)
        } else {
            // Vertical tab
            let tabStart = CGPoint(x: startPoint.x, y: midPoint.y - tabRadius)
            let tabEnd = CGPoint(x: startPoint.x, y: midPoint.y + tabRadius)
            
            path.addLine(to: tabStart)
            path.addArc(withCenter: tabCenter, radius: tabRadius, startAngle: isOutward ? .pi * 1.5 : .pi * 0.5, endAngle: isOutward ? .pi * 0.5 : .pi * 1.5, clockwise: !isOutward)
            path.addLine(to: endPoint)
        }
    }
}
