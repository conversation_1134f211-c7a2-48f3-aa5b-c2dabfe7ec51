//
//  GameScene.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import GameplayKit
import UIKit

class GameScene: SKScene {

    // MARK: - Properties
    private var puzzlePieces: [PuzzlePiece] = []
    private var selectedPiece: PuzzlePiece?
    private var puzzleBoard: SKNode!
    private var piecesContainer: SKNode!

    // Puzzle configuration
    private let gridSize = 2 // 2x2 = 4 pieces for now
    private var pieceSize: CGFloat = 80
    private var boardSize: CGFloat = 180

    // MARK: - Scene Setup
    override func didMove(to view: SKView) {
        print("🎮 GameScene didMove to view")
        print("📏 Scene size: \(size)")
        print("🖼️ View bounds: \(view.bounds)")

        // Enable user interaction
        isUserInteractionEnabled = true
        print("✅ User interaction enabled: \(isUserInteractionEnabled)")

        setupScene()
        createPuzzle()
    }

    private func setupScene() {
        backgroundColor = SKColor(red: 0.15, green: 0.15, blue: 0.3, alpha: 1.0)

        // Set anchor point to bottom-left for easier positioning
        anchorPoint = CGPoint(x: 0.5, y: 0.5)

        // Calculate responsive sizes
        let screenWidth = size.width
        let screenHeight = size.height
        pieceSize = min(screenWidth, screenHeight) * 0.12
        boardSize = pieceSize * CGFloat(gridSize) + 20

        print("🎮 Scene size: \(screenWidth) x \(screenHeight)")
        print("🧩 Piece size: \(pieceSize)")

        // Create puzzle board (upper area)
        puzzleBoard = SKNode()
        puzzleBoard.position = CGPoint(x: 0, y: screenHeight * 0.2)
        addChild(puzzleBoard)

        // Add board background
        let boardBg = SKShapeNode(rectOf: CGSize(width: boardSize, height: boardSize))
        boardBg.fillColor = .clear
        boardBg.strokeColor = .white
        boardBg.lineWidth = 2
        puzzleBoard.addChild(boardBg)

        // Create pieces container (lower area)
        piecesContainer = SKNode()
        piecesContainer.position = CGPoint(x: 0, y: -screenHeight * 0.2)
        addChild(piecesContainer)

        // Add title
        let title = SKLabelNode(text: "PuzzleZone")
        title.fontName = "Helvetica-Bold"
        title.fontSize = 28
        title.fontColor = .white
        title.position = CGPoint(x: 0, y: screenHeight * 0.4)
        addChild(title)

        // Add instruction
        let instruction = SKLabelNode(text: "Parçaları sürükleyin!")
        instruction.fontName = "Helvetica"
        instruction.fontSize = 16
        instruction.fontColor = .lightGray
        instruction.position = CGPoint(x: 0, y: -screenHeight * 0.4)
        addChild(instruction)

        print("📍 Board at: \(puzzleBoard.position)")
        print("📍 Pieces at: \(piecesContainer.position)")
    }


    // MARK: - Puzzle Creation
    private func createPuzzle() {
        // Create a simple colored puzzle for now
        createColoredPuzzle()
        scramblePieces()
    }

    private func createColoredPuzzle() {
        let colors: [UIColor] = [.red, .blue, .green, .yellow]

        print("🎨 Creating \(gridSize)x\(gridSize) puzzle with piece size: \(pieceSize)")

        for row in 0..<gridSize {
            for col in 0..<gridSize {
                let pieceIndex = row * gridSize + col
                let color = colors[pieceIndex % colors.count]

                // Create texture from color
                let texture = createTextureFromColor(color, size: CGSize(width: pieceSize, height: pieceSize))

                // Create puzzle piece
                let piece = PuzzlePiece(texture: texture, row: row, col: col, pieceID: pieceIndex)
                piece.size = CGSize(width: pieceSize, height: pieceSize)

                puzzlePieces.append(piece)
                piecesContainer.addChild(piece)

                print("✅ Created piece \(pieceIndex) (\(color)) at row:\(row) col:\(col)")
            }
        }

        print("📦 Total pieces created: \(puzzlePieces.count)")
        print("📍 Pieces container position: \(piecesContainer.position)")
    }

    private func createTextureFromColor(_ color: UIColor, size: CGSize) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))

            // Add some pattern to make pieces distinguishable
            UIColor.white.withAlphaComponent(0.3).setStroke()
            let path = UIBezierPath()
            path.move(to: CGPoint(x: 0, y: size.height/2))
            path.addLine(to: CGPoint(x: size.width, y: size.height/2))
            path.move(to: CGPoint(x: size.width/2, y: 0))
            path.addLine(to: CGPoint(x: size.width/2, y: size.height))
            path.lineWidth = 2
            path.stroke()
        }
        return SKTexture(image: image)
    }

    private func scramblePieces() {
        let spacing: CGFloat = 20

        print("🔄 Arranging pieces in container")

        for (index, piece) in puzzlePieces.enumerated() {
            // Arrange pieces in a grid within the container
            let col = index % 2
            let row = index / 2

            let x = (CGFloat(col) - 0.5) * (pieceSize + spacing)
            let y = (CGFloat(row) - 0.5) * (pieceSize + spacing)

            piece.position = CGPoint(x: x, y: y)
            piece.setOriginalPosition(piece.position)
            piece.zPosition = CGFloat(index)

            print("🧩 Piece \(index) at: \(piece.position)")
        }
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        print("👆 Touch began at: \(location)")

        // Check all puzzle pieces for touch
        var closestPiece: PuzzlePiece?
        var closestDistance: CGFloat = CGFloat.greatestFiniteMagnitude

        for piece in puzzlePieces {
            // Get the piece's position in scene coordinates
            let pieceScenePosition = convert(piece.position, from: piece.parent!)
            let distance = sqrt(pow(pieceScenePosition.x - location.x, 2) +
                              pow(pieceScenePosition.y - location.y, 2))

            print("🧩 Piece \(piece.pieceID) at scene pos: \(pieceScenePosition), distance: \(distance)")

            // Check if touch is within piece bounds with some tolerance
            if distance < pieceSize/2 + 10 && distance < closestDistance {
                closestPiece = piece
                closestDistance = distance
            }
        }

        if let piece = closestPiece {
            selectedPiece = piece
            piece.highlight()
            piece.zPosition = 1000
            print("🎯 Selected piece: \(piece.pieceID)")
        } else {
            print("❌ No piece selected")
        }
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first,
              let piece = selectedPiece else { return }

        let location = touch.location(in: self)

        // Convert the touch location to the piece's parent coordinate system
        if let parent = piece.parent {
            let locationInParent = convert(location, to: parent)
            piece.position = locationInParent
            print("🚚 Moving piece \(piece.pieceID) to parent coords: \(locationInParent) (scene: \(location))")
        } else {
            piece.position = location
            print("🚚 Moving piece \(piece.pieceID) to scene coords: \(location)")
        }
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let piece = selectedPiece else { return }

        print("🏁 Touch ended with piece: \(piece.pieceID)")

        piece.unhighlight()
        piece.zPosition = CGFloat(piece.pieceID)

        // Convert board position to piece's parent coordinate system for accurate checking
        let boardPositionInPieceParent = convert(puzzleBoard.position, to: piece.parent!)

        // Check if piece is in correct position
        if piece.checkIfInCorrectPosition(boardPosition: boardPositionInPieceParent, pieceSize: pieceSize) {
            print("✅ Piece \(piece.pieceID) is in correct position!")
            piece.snapToCorrectPosition(boardPosition: boardPositionInPieceParent, pieceSize: pieceSize)
            checkPuzzleCompletion()
        } else {
            print("❌ Piece \(piece.pieceID) not in correct position, returning to original")
            piece.returnToOriginalPosition()
        }

        selectedPiece = nil
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        selectedPiece?.unhighlight()
        selectedPiece?.returnToOriginalPosition()
        selectedPiece = nil
    }

    // MARK: - Game Logic
    private func checkPuzzleCompletion() {
        let completedPieces = puzzlePieces.filter { $0.isCorrectlyPlaced }

        if completedPieces.count == puzzlePieces.count {
            puzzleCompleted()
        }
    }

    private func puzzleCompleted() {
        // Haptic feedback for completion
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)

        // Visual celebration
        let celebrationLabel = SKLabelNode(text: "Tebrikler! 🎉")
        celebrationLabel.fontName = "Helvetica-Bold"
        celebrationLabel.fontSize = 28
        celebrationLabel.fontColor = .yellow
        celebrationLabel.position = CGPoint(x: 0, y: 0)
        addChild(celebrationLabel)

        // Animate celebration
        let scaleUp = SKAction.scale(to: 1.2, duration: 0.3)
        let scaleDown = SKAction.scale(to: 1.0, duration: 0.3)
        let sequence = SKAction.sequence([scaleUp, scaleDown])
        celebrationLabel.run(SKAction.repeat(sequence, count: 3))

        // Remove after animation
        let wait = SKAction.wait(forDuration: 2.0)
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let remove = SKAction.removeFromParent()
        celebrationLabel.run(SKAction.sequence([wait, fadeOut, remove]))
    }
}
