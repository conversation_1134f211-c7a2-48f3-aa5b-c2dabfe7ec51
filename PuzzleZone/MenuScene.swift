//
//  MenuScene.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

class MenuScene: SKScene {

    // MARK: - Properties
    private var titleLabel: SKLabelNode!
    private var subtitleLabel: SKLabelNode!
    private var difficultyButtons: [SKNode] = []

    // MARK: - Scene Setup
    override func didMove(to view: SKView) {
        setupScene()
        createMenu()
    }

    private func setupScene() {
        // Set background gradient
        backgroundColor = SKColor(red: 0.1, green: 0.1, blue: 0.2, alpha: 1.0)

        // Set anchor point to center
        anchorPoint = CGPoint(x: 0.5, y: 0.5)

        // Add background pattern
        addBackgroundPattern()
    }

    private func addBackgroundPattern() {
        // Add subtle Turkish pattern background
        for i in 0..<5 {
            for j in 0..<8 {
                let pattern = SKShapeNode(circleOfRadius: 3)
                pattern.fillColor = UIColor.white.withAlphaComponent(0.05)
                pattern.strokeColor = .clear
                pattern.position = CGPoint(
                    x: CGFloat(i - 2) * 80,
                    y: CGFloat(j - 4) * 80
                )
                addChild(pattern)
            }
        }
    }

    private func createMenu() {
        // Title
        titleLabel = SKLabelNode(text: "PuzzleZone")
        titleLabel.fontName = "Helvetica-Bold"
        titleLabel.fontSize = 48
        titleLabel.fontColor = .white
        titleLabel.position = CGPoint(x: 0, y: size.height * 0.25)
        addChild(titleLabel)

        // Subtitle
        subtitleLabel = SKLabelNode(text: "Türk Kültürü Puzzle Oyunu")
        subtitleLabel.fontName = "Helvetica"
        subtitleLabel.fontSize = 18
        subtitleLabel.fontColor = .lightGray
        subtitleLabel.position = CGPoint(x: 0, y: size.height * 0.18)
        addChild(subtitleLabel)

        // Difficulty selection
        let difficultyLabel = SKLabelNode(text: "Zorluk Seviyesi Seçin:")
        difficultyLabel.fontName = "Helvetica-Bold"
        difficultyLabel.fontSize = 24
        difficultyLabel.fontColor = .white
        difficultyLabel.position = CGPoint(x: 0, y: size.height * 0.05)
        addChild(difficultyLabel)

        // Create difficulty buttons
        createDifficultyButtons()

        // Add title animation
        animateTitle()
    }

    private func createDifficultyButtons() {
        let difficulties = [
            ("Kolay", "10 Parça", 10, UIColor.green),
            ("Orta", "20 Parça", 20, UIColor.orange),
            ("Zor", "30 Parça", 30, UIColor.red)
        ]

        let buttonSpacing: CGFloat = 120
        let startY: CGFloat = -size.height * 0.1

        for (index, difficulty) in difficulties.enumerated() {
            let button = createDifficultyButton(
                title: difficulty.0,
                subtitle: difficulty.1,
                pieceCount: difficulty.2,
                color: difficulty.3,
                position: CGPoint(x: 0, y: startY - CGFloat(index) * buttonSpacing)
            )
            difficultyButtons.append(button)
            addChild(button)
        }
    }

    private func createDifficultyButton(title: String, subtitle: String, pieceCount: Int, color: UIColor, position: CGPoint) -> SKNode {
        let buttonContainer = SKNode()
        buttonContainer.position = position
        buttonContainer.name = "difficulty_\(pieceCount)"

        // Button background
        let buttonBg = SKShapeNode(rectOf: CGSize(width: 200, height: 80), cornerRadius: 15)
        buttonBg.fillColor = color.withAlphaComponent(0.8)
        buttonBg.strokeColor = color
        buttonBg.lineWidth = 3
        buttonContainer.addChild(buttonBg)

        // Title label
        let titleLabel = SKLabelNode(text: title)
        titleLabel.fontName = "Helvetica-Bold"
        titleLabel.fontSize = 22
        titleLabel.fontColor = .white
        titleLabel.position = CGPoint(x: 0, y: 10)
        titleLabel.verticalAlignmentMode = .center
        buttonContainer.addChild(titleLabel)

        // Subtitle label
        let subtitleLabel = SKLabelNode(text: subtitle)
        subtitleLabel.fontName = "Helvetica"
        subtitleLabel.fontSize = 16
        subtitleLabel.fontColor = .white
        subtitleLabel.position = CGPoint(x: 0, y: -15)
        subtitleLabel.verticalAlignmentMode = .center
        buttonContainer.addChild(subtitleLabel)

        // Add glow effect
        let glow = SKShapeNode(rectOf: CGSize(width: 210, height: 90), cornerRadius: 20)
        glow.fillColor = .clear
        glow.strokeColor = color.withAlphaComponent(0.3)
        glow.lineWidth = 5
        glow.zPosition = -1
        buttonContainer.addChild(glow)

        return buttonContainer
    }

    private func animateTitle() {
        let scaleUp = SKAction.scale(to: 1.1, duration: 1.0)
        let scaleDown = SKAction.scale(to: 1.0, duration: 1.0)
        let sequence = SKAction.sequence([scaleUp, scaleDown])
        let repeatAction = SKAction.repeatForever(sequence)
        titleLabel.run(repeatAction)
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        // Check if any difficulty button was touched
        for button in difficultyButtons {
            if button.contains(location) {
                animateButtonPress(button)

                // Extract piece count from button name
                if let name = button.name,
                   let pieceCountString = name.components(separatedBy: "_").last,
                   let pieceCount = Int(pieceCountString) {

                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()

                    // Start game with selected difficulty
                    startGame(with: pieceCount)
                }
                break
            }
        }
    }

    private func animateButtonPress(_ button: SKNode) {
        let scaleDown = SKAction.scale(to: 0.9, duration: 0.1)
        let scaleUp = SKAction.scale(to: 1.0, duration: 0.1)
        let sequence = SKAction.sequence([scaleDown, scaleUp])
        button.run(sequence)
    }

    private func startGame(with pieceCount: Int) {
        print("🎮 Starting game with piece count: \(pieceCount)")

        // Create game scene with selected difficulty
        let gameScene = GameScene(pieceCount: pieceCount)
        gameScene.size = size
        gameScene.scaleMode = scaleMode

        // Transition to game scene
        let transition = SKTransition.fade(withDuration: 0.5)
        view?.presentScene(gameScene, transition: transition)
    }
}
