//
//  PuzzlePiece.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

class PuzzlePiece: SKSpriteNode {

    // MARK: - Properties
    let correctRow: Int
    let correctCol: Int
    let pieceID: Int

    private var originalPosition: CGPoint = .zero
    private var isInCorrectPosition = false
    private let snapDistance: CGFloat = 30

    // MARK: - Initialization
    init(texture: SKTexture?, row: Int, col: Int, pieceID: Int) {
        self.correctRow = row
        self.correctCol = col
        self.pieceID = pieceID

        super.init(texture: texture, color: .clear, size: texture?.size() ?? CGSize(width: 100, height: 100))

        setupPiece()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupPiece() {
        // Don't enable user interaction on individual pieces - let the scene handle all touches
        isUserInteractionEnabled = false
        name = "puzzlePiece_\(pieceID)"

        // Add border for better visibility
        let border = SKShapeNode(rectOf: size)
        border.strokeColor = .white
        border.fillColor = .clear
        border.lineWidth = 2
        border.alpha = 0.7
        addChild(border)

        // Add shadow effect
        let shadow = SKShapeNode(rectOf: size)
        shadow.fillColor = .black
        shadow.alpha = 0.3
        shadow.position = CGPoint(x: 2, y: -2)
        shadow.zPosition = -1
        addChild(shadow)
    }

    // MARK: - Position Management
    func setOriginalPosition(_ position: CGPoint) {
        originalPosition = position
    }

    func getCorrectPosition(boardPosition: CGPoint, pieceSize: CGFloat) -> CGPoint {
        let offsetX = (CGFloat(correctCol) - 0.5) * pieceSize
        let offsetY = (0.5 - CGFloat(correctRow)) * pieceSize
        return CGPoint(x: boardPosition.x + offsetX, y: boardPosition.y + offsetY)
    }

    func checkIfInCorrectPosition(boardPosition: CGPoint, pieceSize: CGFloat) -> Bool {
        let correctPos = getCorrectPosition(boardPosition: boardPosition, pieceSize: pieceSize)
        let distance = sqrt(pow(position.x - correctPos.x, 2) + pow(position.y - correctPos.y, 2))
        return distance < snapDistance
    }

    func snapToCorrectPosition(boardPosition: CGPoint, pieceSize: CGFloat) {
        let correctPos = getCorrectPosition(boardPosition: boardPosition, pieceSize: pieceSize)

        let moveAction = SKAction.move(to: correctPos, duration: 0.2)
        moveAction.timingMode = .easeOut

        run(moveAction) { [weak self] in
            self?.isInCorrectPosition = true
            self?.triggerHapticFeedback()
            print("✅ Piece \(self?.pieceID ?? 0) marked as correctly placed!")
        }
    }

    func returnToOriginalPosition() {
        let moveAction = SKAction.move(to: originalPosition, duration: 0.3)
        moveAction.timingMode = .easeOut
        run(moveAction)
        isInCorrectPosition = false
        print("❌ Piece \(pieceID) returned to original position")
    }

    // MARK: - Visual Effects
    func highlight() {
        let scaleUp = SKAction.scale(to: 1.1, duration: 0.1)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 0.1)
        let group = SKAction.group([scaleUp, fadeIn])
        run(group)
    }

    func unhighlight() {
        let scaleDown = SKAction.scale(to: 1.0, duration: 0.1)
        let fadeOut = SKAction.fadeAlpha(to: 0.9, duration: 0.1)
        let group = SKAction.group([scaleDown, fadeOut])
        run(group)
    }

    // MARK: - Haptic Feedback
    private func triggerHapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    // MARK: - Status
    var isCorrectlyPlaced: Bool {
        return isInCorrectPosition
    }
}
