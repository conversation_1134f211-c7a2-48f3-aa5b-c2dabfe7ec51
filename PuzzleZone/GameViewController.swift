//
//  GameViewController.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import UIKit
import SpriteKit
import GameplayKit

class GameViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        print("🚀 GameViewController viewDidLoad called")
        NSLog("🚀 GameViewController viewDidLoad called")

        if let view = self.view as! SKView? {
            print("📱 SKView found: \(view.bounds)")

            // Show debug info (remove in production)
            view.showsFPS = true
            view.showsNodeCount = true
            view.ignoresSiblingOrder = true

            // Create the game scene immediately
            let scene = GameScene()

            // Set the scene size to match the view
            scene.size = view.bounds.size

            // Set the scale mode to scale to fit the window
            scene.scaleMode = .aspectFill

            // Present the scene
            view.presentScene(scene)

            print("🎮 Scene created and presented with size: \(scene.size)")
            print("🖥️ View bounds: \(view.bounds)")
        } else {
            print("❌ SKView not found!")
        }
    }

    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if UIDevice.current.userInterfaceIdiom == .phone {
            return .allButUpsideDown
        } else {
            return .all
        }
    }

    override var prefersStatusBarHidden: Bool {
        return true
    }
}
